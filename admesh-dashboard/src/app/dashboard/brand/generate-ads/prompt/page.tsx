"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

import { toast } from "sonner";
import { useAuth } from "@/hooks/use-auth";
import { config } from "@/config/environment";
import PlatformPreview from "@/components/PlatformPreview";
import AdDownloadManager from "@/components/AdDownloadManager";
import {
  <PERSON>rk<PERSON>,
  Image as ImageIcon,
  Loader2,
  <PERSON>,
  Download,
  <PERSON><PERSON>,
  Wand2,
  Trash2,
  Cloud,
  HardDrive,
  Target
} from "lucide-react";

interface GeneratedAd {
  id: string;
  type: string;
  content: string;
  headline?: string;
  description?: string;
  cta?: string;
  platform: string;
  format: string;
  media_assets?: Array<{
    id: string;
    type: string;
    url: string;
    storage_path: string;
    metadata: Record<string, any>;
  }>;
  generation_metadata?: Record<string, any>;
}

interface HistoryEntry {
  id: string;
  prompt: string;
  platforms: string[];
  imageStyle: string;
  adsCount: number;
  timestamp: string;
  ads: GeneratedAd[];
}

interface StoredAd {
  id: string;
  platform: string;
  headline: string;
  description: string;
  cta: string;
  content: string;
  media_assets?: Array<{
    url: string;
    type: string;
  }>;
  timestamp: string;
}

export default function PromptAdGeneratorPage() {
  const { user } = useAuth();
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedAds, setGeneratedAds] = useState<GeneratedAd[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(["facebook", "instagram"]);
  const [selectedFormats, setSelectedFormats] = useState<{[key: string]: string}>({
    facebook: "Feed Post",
    instagram: "Feed Post",
    linkedin: "Feed Post",
    google: "Medium Rectangle"
  });
  const [imageStyle, setImageStyle] = useState("creative");
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [activeTab, setActiveTab] = useState("generate");
  const [savedAds, setSavedAds] = useState<StoredAd[]>([]);
  const [savedBatches, setSavedBatches] = useState<any[]>([]);
  const [isLoadingSaved, setIsLoadingSaved] = useState(false);
  const [storageStats, setStorageStats] = useState({ totalAds: 0, totalBatches: 0, storageUsed: 0 });

  const platforms = [
    {
      value: "facebook",
      label: "Facebook",
      emoji: "📘",
      logo: "/logos/facebook.svg",
      brandColor: "#1877f2",
      formats: [
        { name: "Feed Post", dimensions: "1080×1080px", ratio: "1:1", description: "Square posts for news feed", width: 1080, height: 1080 },
        { name: "Story", dimensions: "1080×1920px", ratio: "9:16", description: "Vertical stories format", width: 1080, height: 1920 },
        { name: "Cover Photo", dimensions: "851×315px", ratio: "16:9", description: "Page cover image", width: 851, height: 315 },
        { name: "Shared Link", dimensions: "1200×627px", ratio: "1.91:1", description: "Link preview image", width: 1200, height: 627 }
      ]
    },
    {
      value: "instagram",
      label: "Instagram",
      emoji: "📷",
      logo: "/logos/instagram.svg",
      brandColor: "#E4405F",
      formats: [
        { name: "Feed Post", dimensions: "1080×1080px", ratio: "1:1", description: "Square feed posts", width: 1080, height: 1080 },
        { name: "Portrait Post", dimensions: "1080×1350px", ratio: "4:5", description: "Vertical feed posts", width: 1080, height: 1350 },
        { name: "Story", dimensions: "1080×1920px", ratio: "9:16", description: "Stories and reels", width: 1080, height: 1920 },
        { name: "Landscape", dimensions: "1080×566px", ratio: "1.91:1", description: "Horizontal posts", width: 1080, height: 566 }
      ]
    },
    {
      value: "linkedin",
      label: "LinkedIn",
      emoji: "💼",
      logo: "/logos/linkedin.svg",
      brandColor: "#0077b5",
      formats: [
        { name: "Feed Post", dimensions: "1200×627px", ratio: "1.91:1", description: "Shared content posts", width: 1200, height: 627 },
        { name: "Square Post", dimensions: "1080×1080px", ratio: "1:1", description: "Square image posts", width: 1080, height: 1080 },
        { name: "Portrait Post", dimensions: "1080×1350px", ratio: "4:5", description: "Vertical image posts", width: 1080, height: 1350 },
        { name: "Cover Image", dimensions: "1128×191px", ratio: "5.9:1", description: "Company page cover", width: 1128, height: 191 }
      ]
    },
    {
      value: "google",
      label: "Google Ads",
      emoji: "🔍",
      logo: "/logos/google-ads.svg",
      brandColor: "#4285f4",
      formats: [
        { name: "Display Banner", dimensions: "728×90px", ratio: "8:1", description: "Leaderboard banner", width: 728, height: 90 },
        { name: "Medium Rectangle", dimensions: "300×250px", ratio: "1.2:1", description: "Standard display ad", width: 300, height: 250 },
        { name: "Large Rectangle", dimensions: "336×280px", ratio: "1.2:1", description: "Large display ad", width: 336, height: 280 },
        { name: "Skyscraper", dimensions: "160×600px", ratio: "4:15", description: "Vertical banner ad", width: 160, height: 600 },
        { name: "Responsive Display", dimensions: "1200×628px", ratio: "1.91:1", description: "Responsive display ad", width: 1200, height: 628 }
      ]
    }
  ];

  const imageStyles = [
    { value: "creative", label: "Creative" },
    { value: "bold", label: "Bold" },
    { value: "minimalist", label: "Minimal" },
    { value: "professional", label: "Professional" }
  ];

  const promptExamples = [
    "Revolutionary AI-powered fitness app that reads your mind",
    "Eco-friendly sneakers made from ocean plastic",
    "Premium coffee that gives you superpowers",
    "Dating app for introverts",
    "Time-travel vacation packages"
  ];

  useEffect(() => {
    loadHistory();
  }, []);

  const generateAds = async () => {
    if (!prompt.trim()) {
      toast.error("Please enter a prompt for your ad");
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast.error("Please select at least one platform");
      return;
    }

    setIsGenerating(true);

    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) {
        toast.error("Please log in to generate ads");
        return;
      }

      const token = await getIdToken(auth.currentUser);
      const allGeneratedAds: GeneratedAd[] = [];

      // If multiple platforms selected, use cross-platform generation
      if (selectedPlatforms.length > 1) {
        await generateCrossPlatformAds(token, allGeneratedAds);
      } else {
        // Single platform generation (existing logic)
        await generateSinglePlatformAd(token, selectedPlatforms[0], allGeneratedAds);
      }

      if (allGeneratedAds.length > 0) {
        setGeneratedAds(allGeneratedAds);
        toast.success(`Generated ${allGeneratedAds.length} ads successfully! Ads are automatically saved.`);

        // Reload saved ads to update the list (ads are saved automatically by backend)
        await loadSavedAds();

        // Add to local history
        const historyEntry: HistoryEntry = {
          id: Date.now().toString(),
          prompt: prompt,
          platforms: selectedPlatforms,
          imageStyle: imageStyle,
          adsCount: allGeneratedAds.length,
          timestamp: new Date().toISOString(),
          ads: allGeneratedAds
        };
        setHistory(prev => [historyEntry, ...prev]);
        setActiveTab("results");
      } else {
        throw new Error("Failed to generate any ads");
      }
    } catch (error) {
      console.error("Error generating ads:", error);
      toast.error(error instanceof Error ? error.message : "Failed to generate ads");
    } finally {
      setIsGenerating(false);
    }
  };

  const generateCrossPlatformAds = async (token: string, allGeneratedAds: GeneratedAd[]) => {
    // Generate a base ad for the first platform
    const basePlatform = selectedPlatforms[0];
    const basePlatformData = platforms.find(p => p.value === basePlatform);
    const baseFormat = selectedFormats[basePlatform];
    const baseFormatData = basePlatformData?.formats.find(f => f.name === baseFormat);

    if (!baseFormatData) {
      throw new Error(`Format not found for base platform ${basePlatform}: ${baseFormat}`);
    }

    // Generate base ad
    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
    const baseResponse = await fetch(`${apiBaseUrl}/ad-generation/generate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      },
      body: JSON.stringify({
        product_name: extractProductName(prompt),
        product_description: prompt,
        target_audience: "General audience",
        ad_type: "multimodal",
        platform: basePlatform,
        format: baseFormat,
        tone: "creative",
        keywords: "",
        cta: "Learn More",
        additional_info: `Create ${baseFormatData.name} for ${basePlatformData.label}. This will be adapted for multiple platforms. Make it creative and eye-catching!`,
        num_variations: 1,
        generate_images: true,
        generate_videos: false,
        image_style: imageStyle,
        video_duration: 15,
        brand_colors: [],
        brand_fonts: [],
        use_brand_assets: true,
        image_width: baseFormatData.width,
        image_height: baseFormatData.height,
        aspect_ratio: baseFormatData.ratio
      })
    });

    if (!baseResponse.ok) {
      throw new Error("Failed to generate base ad");
    }

    const baseData = await baseResponse.json();
    if (!baseData.success || !baseData.ads || baseData.ads.length === 0) {
      throw new Error("No base ad generated");
    }

    const baseAd = baseData.ads[0];
    allGeneratedAds.push(baseAd);

    // Now adapt for other platforms
    const otherPlatforms = selectedPlatforms.slice(1);
    if (otherPlatforms.length > 0) {
      for (const platform of otherPlatforms) {
        try {
          await generateAdaptedAd(token, baseAd, platform, allGeneratedAds);
        } catch (error) {
          console.error(`Error adapting ad for ${platform}:`, error);
        }
      }
    }
  };

  const generateAdaptedAd = async (token: string, baseAd: any, targetPlatform: string, allGeneratedAds: GeneratedAd[]) => {
    const platformData = platforms.find(p => p.value === targetPlatform);
    const selectedFormat = selectedFormats[targetPlatform];
    const formatData = platformData?.formats.find(f => f.name === selectedFormat);

    if (!formatData) {
      console.error(`Format not found for ${targetPlatform}: ${selectedFormat}`);
      return;
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';

    // Generate adapted ad with platform-specific optimization
    const response = await fetch(`${apiBaseUrl}/ad-generation/generate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      },
      body: JSON.stringify({
        product_name: extractProductName(prompt),
        product_description: prompt,
        target_audience: "General audience",
        ad_type: "multimodal",
        platform: targetPlatform,
        format: selectedFormat,
        tone: "creative",
        keywords: "",
        cta: baseAd.cta || "Learn More",
        additional_info: `Adapt this ad for ${platformData.label} ${formatData.name}.
        Original content: "${baseAd.content || baseAd.description}"
        Original headline: "${baseAd.headline}"
        Maintain brand consistency while optimizing for ${targetPlatform} best practices.
        Exact dimensions: ${formatData.dimensions} (${formatData.ratio} ratio).`,
        num_variations: 1,
        generate_images: true,
        generate_videos: false,
        image_style: imageStyle,
        video_duration: 15,
        brand_colors: [],
        brand_fonts: [],
        use_brand_assets: true,
        image_width: formatData.width,
        image_height: formatData.height,
        aspect_ratio: formatData.ratio,
        // Reference to base ad for consistency
        base_ad_reference: {
          headline: baseAd.headline,
          description: baseAd.description || baseAd.content,
          cta: baseAd.cta
        }
      })
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.ads) {
        allGeneratedAds.push(...data.ads);
      }
    }
  };

  const generateSinglePlatformAd = async (token: string, platform: string, allGeneratedAds: GeneratedAd[]) => {
    const platformData = platforms.find(p => p.value === platform);
    const selectedFormat = selectedFormats[platform];
    const formatData = platformData?.formats.find(f => f.name === selectedFormat);

    if (!formatData) {
      throw new Error(`Format not found for ${platform}: ${selectedFormat}`);
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
    const response = await fetch(`${apiBaseUrl}/ad-generation/generate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      },
      body: JSON.stringify({
        product_name: extractProductName(prompt),
        product_description: prompt,
        target_audience: "General audience",
        ad_type: "multimodal",
        platform: platform,
        format: selectedFormat,
        tone: "creative",
        keywords: "",
        cta: "Learn More",
        additional_info: `Create ${formatData.name} for ${platformData.label}. Exact dimensions: ${formatData.dimensions} (${formatData.ratio} ratio). ${formatData.description}. Make it creative and eye-catching!`,
        num_variations: 2,
        generate_images: true,
        generate_videos: false,
        image_style: imageStyle,
        video_duration: 15,
        brand_colors: [],
        brand_fonts: [],
        use_brand_assets: true,
        image_width: formatData.width,
        image_height: formatData.height,
        aspect_ratio: formatData.ratio
      })
    });

    if (response.ok) {
      const data = await response.json();
      if (data.success && data.ads) {
        allGeneratedAds.push(...data.ads);
      }
    } else {
      const errorData = await response.json();
      throw new Error(`API Error for ${platform}: ${errorData.detail || 'Unknown error'}`);
    }
  };

  const extractProductName = (prompt: string): string => {
    const words = prompt.split(' ');
    if (words.length <= 3) return prompt;

    const productIndicators = ['for', 'about', 'selling', 'promoting'];
    for (const indicator of productIndicators) {
      const index = words.findIndex(word => word.toLowerCase().includes(indicator));
      if (index > 0) {
        return words.slice(0, index).join(' ');
      }
    }

    return words.slice(0, 3).join(' ');
  };

  const loadHistory = async () => {
    try {
      const { getIdToken } = await import("firebase/auth");
      const { auth } = await import("@/lib/firebase");

      if (!auth.currentUser) return;

      const token = await getIdToken(auth.currentUser);
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${apiBaseUrl}/ad-generation/history?limit=20`, {
        headers: {
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.history) {
          setHistory(data.history);
        }
      }
    } catch (error) {
      console.error("Error loading history:", error);
    }
  };

  const handlePlatformToggle = (platform: string) => {
    setSelectedPlatforms(prev =>
      prev.includes(platform)
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    );
  };

  // Load saved ads from backend API
  const loadSavedAds = async () => {
    setIsLoadingSaved(true);
    try {
      const token = await user?.getIdToken();
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${apiBaseUrl}/ad-generation/saved-ads`, {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSavedBatches(data.batches || []);

        // Flatten all ads from batches
        const allAds = data.batches?.flatMap((batch: any) => batch.ads) || [];
        setSavedAds(allAds);
      } else {
        throw new Error("Failed to load saved ads");
      }

      // Load storage stats
      const statsResponse = await fetch(`${apiBaseUrl}/ad-generation/storage-stats`, {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStorageStats(statsData.stats || { totalAds: 0, totalBatches: 0, storageUsed: 0 });
      }
    } catch (error) {
      console.error("Error loading saved ads:", error);
    } finally {
      setIsLoadingSaved(false);
    }
  };

  // Delete a batch of ads
  const handleDeleteBatch = async (batchId: string) => {
    try {
      const token = await user?.getIdToken();
      const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://127.0.0.1:8000';
      const response = await fetch(`${apiBaseUrl}/ad-generation/saved-ads/${batchId}`, {
        method: "DELETE",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        toast.success("Batch deleted successfully");
        // Reload saved ads
        await loadSavedAds();
      } else {
        throw new Error("Failed to delete batch");
      }
    } catch (error) {
      console.error("Error deleting batch:", error);
      toast.error("Failed to delete batch");
    }
  };

  // Load saved ads on component mount
  useEffect(() => {
    if (user) {
      loadSavedAds();
    }
  }, [user]);

  const copyAdContent = (ad: GeneratedAd | StoredAd) => {
    const content = `${ad.headline}\n\n${ad.description}\n\n${ad.cta}`;
    navigator.clipboard.writeText(content);
    toast.success("Ad content copied to clipboard!");
  };

  // Function to convert Firebase storage path to download URL
  const getImageUrl = (mediaAsset: any) => {
    if (!mediaAsset) return null;

    // If it's already a full URL, return it
    if (mediaAsset.url && (mediaAsset.url.startsWith('http') || mediaAsset.url.startsWith('https'))) {
      return mediaAsset.url;
    }

    // If it's a Firebase storage path, convert it using dynamic project ID
    if (mediaAsset.storage_path) {
      // Use the current environment's Firebase project ID
      const projectId = config.firebase.projectId;

      // Convert Firebase storage path to download URL with dynamic project ID
      const baseUrl = `https://firebasestorage.googleapis.com/v0/b/${projectId}.appspot.com/o/`;
      const encodedPath = encodeURIComponent(mediaAsset.storage_path);
      const downloadUrl = `${baseUrl}${encodedPath}?alt=media`;

      console.log(`🔥 Converting storage path to URL:`, {
        environment: config.environment,
        projectId,
        storagePath: mediaAsset.storage_path,
        downloadUrl
      });

      return downloadUrl;
    }

    // Fallback to the original URL
    return mediaAsset.url;
  };

  const loadFromHistory = (entry: HistoryEntry) => {
    setPrompt(entry.prompt);
    setSelectedPlatforms(entry.platforms);
    setImageStyle(entry.imageStyle);
    setGeneratedAds(entry.ads);
    setActiveTab("results");
    toast.success("Loaded from history!");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Simple Header */}
      <div className="bg-white border-b">
        <div className="px-6 py-4">
          <div className="max-w-4xl mx-auto">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Wand2 className="h-5 w-5 text-gray-600" />
                <h1 className="text-xl font-semibold text-gray-900">
                  AI Ad Generator
                </h1>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setActiveTab(activeTab === "history" ? "generate" : "history")}
              >
                <History className="h-4 w-4 mr-1" />
                History
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-6 py-6">
        <div className="max-w-4xl mx-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="generate">Generate</TabsTrigger>
              <TabsTrigger value="results">Results ({generatedAds.length})</TabsTrigger>
              <TabsTrigger value="saved">
                Saved ({savedBatches.length})
              </TabsTrigger>
              <TabsTrigger value="history">History</TabsTrigger>
            </TabsList>

            <TabsContent value="generate" className="space-y-6">
              {/* Prompt Input */}
              <Card>
                <CardHeader>
                  <CardTitle>What do you want to advertise?</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder="Describe your product or service..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="min-h-[100px]"
                  />

                  {/* Example prompts */}
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Examples:</p>
                    <div className="flex flex-wrap gap-2">
                      {promptExamples.map((example, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          onClick={() => setPrompt(example)}
                          className="text-xs h-8"
                        >
                          {example.slice(0, 30)}...
                        </Button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Platform & Format Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Select Platforms & Formats
                  </CardTitle>
                  <p className="text-sm text-gray-600">Choose platforms and exact image dimensions for your ads</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {platforms.map((platform) => {
                    const isSelected = selectedPlatforms.includes(platform.value);
                    return (
                      <div key={platform.value} className="space-y-3">
                        {/* Platform Toggle */}
                        <div
                          className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            isSelected
                              ? "border-purple-500 bg-purple-50"
                              : "border-gray-200 hover:border-gray-300"
                          }`}
                          onClick={() => {
                            if (isSelected) {
                              setSelectedPlatforms(selectedPlatforms.filter(p => p !== platform.value));
                            } else {
                              setSelectedPlatforms([...selectedPlatforms, platform.value]);
                            }
                          }}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <Checkbox checked={isSelected} readOnly />
                              <span className="text-lg">{platform.emoji}</span>
                              <span className="font-medium">{platform.label}</span>
                            </div>
                            {isSelected && (
                              <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                                {platform.formats.length} formats available
                              </Badge>
                            )}
                          </div>
                        </div>

                        {/* Format Selection (only show if platform is selected) */}
                        {isSelected && (
                          <div className="ml-6 space-y-2">
                            <Label className="text-sm font-medium text-gray-700">
                              Choose format for {platform.label}:
                            </Label>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {platform.formats.map((format) => {
                                const isFormatSelected = selectedFormats[platform.value] === format.name;
                                return (
                                  <div
                                    key={format.name}
                                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                                      isFormatSelected
                                        ? "border-blue-500 bg-blue-50"
                                        : "border-gray-200 hover:border-blue-200"
                                    }`}
                                    onClick={() => {
                                      setSelectedFormats(prev => ({
                                        ...prev,
                                        [platform.value]: format.name
                                      }));
                                    }}
                                  >
                                    <div className="space-y-1">
                                      <div className="flex items-center justify-between">
                                        <span className="font-medium text-sm">{format.name}</span>
                                        <Badge variant="outline" className="text-xs">
                                          {format.ratio}
                                        </Badge>
                                      </div>
                                      <div className="text-xs text-gray-600">
                                        {format.dimensions}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        {format.description}
                                      </div>
                                    </div>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </CardContent>
              </Card>

              {/* Image Style */}
              <Card>
                <CardHeader>
                  <CardTitle>Image Style</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {imageStyles.map((style) => (
                      <Button
                        key={style.value}
                        variant={imageStyle === style.value ? "default" : "outline"}
                        onClick={() => setImageStyle(style.value)}
                        className="h-auto p-3"
                      >
                        {style.label}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Generate Button */}
              <div className="flex justify-center">
                <Button
                  onClick={generateAds}
                  disabled={!prompt.trim() || selectedPlatforms.length === 0 || isGenerating}
                  size="lg"
                  className="min-w-[200px]"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Wand2 className="h-4 w-4 mr-2" />
                      Generate Ads
                    </>
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="results" className="space-y-6">
              {generatedAds.length > 0 ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {generatedAds.map((ad) => {
                    const platformData = platforms.find(p => p.value === ad.platform);
                    const formatData = platformData?.formats.find(f => f.name === selectedFormats[ad.platform]);

                    return (
                      <Card key={ad.id} className="overflow-hidden">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <span className="text-lg">{platformData?.emoji}</span>
                              <div>
                                <Badge variant="outline" className="capitalize">
                                  {ad.platform}
                                </Badge>
                                {formatData && (
                                  <div className="text-xs text-gray-500 mt-1">
                                    {formatData.name} • {formatData.dimensions}
                                  </div>
                                )}
                              </div>
                            </div>
                            <div className="flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyAdContent(ad)}
                                title="Copy ad content"
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              {ad.media_assets && ad.media_assets.length > 0 && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    const imageUrl = getImageUrl(ad.media_assets[0]);
                                    if (imageUrl) {
                                      const link = document.createElement('a');
                                      link.href = imageUrl;
                                      link.download = `${ad.platform}-${formatData?.name || 'ad'}-${ad.id}.jpg`;
                                      link.target = '_blank';
                                      link.click();
                                    } else {
                                      toast.error('Image URL not available for download');
                                    }
                                  }}
                                  title="Download image"
                                >
                                  <Download className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          {/* Generated Image with Exact Aspect Ratio */}
                          {ad.media_assets && ad.media_assets.length > 0 ? (
                            <div className="relative">
                              <div
                                className="rounded-lg overflow-hidden bg-gray-100 border"
                                style={{
                                  aspectRatio: formatData?.ratio || '1:1'
                                }}
                              >
                                {(() => {
                                  const imageUrl = getImageUrl(ad.media_assets[0]);
                                  console.log('Processed image URL:', imageUrl, 'from:', ad.media_assets[0]);
                                  return (
                                    <img
                                      src={imageUrl || ''}
                                      alt={ad.headline || "Generated ad image"}
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        console.error('Image failed to load:', imageUrl);
                                        console.log('Original media asset:', ad.media_assets[0]);
                                        e.currentTarget.style.display = 'none';
                                        e.currentTarget.nextElementSibling?.classList.remove('hidden');
                                      }}
                                    />
                                  );
                                })()}
                                {/* Fallback when image fails to load */}
                                <div className="hidden w-full h-full flex items-center justify-center bg-gray-200 text-gray-500">
                                  <div className="text-center">
                                    <ImageIcon className="h-8 w-8 mx-auto mb-2" />
                                    <p className="text-xs">Image not available</p>
                                    <p className="text-xs mt-1 break-all">URL: {getImageUrl(ad.media_assets[0])}</p>
                                    <p className="text-xs mt-1 break-all">Storage: {ad.media_assets[0].storage_path}</p>
                                  </div>
                                </div>
                              </div>
                              {/* Dimension Overlay */}
                              <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                                {formatData?.dimensions}
                              </div>
                            </div>
                          ) : (
                            <div
                              className="rounded-lg overflow-hidden bg-gray-200 border flex items-center justify-center"
                              style={{
                                aspectRatio: formatData?.ratio || '1:1'
                              }}
                            >
                              <div className="text-center text-gray-500">
                                <ImageIcon className="h-8 w-8 mx-auto mb-2" />
                                <p className="text-xs">No image generated</p>
                              </div>
                            </div>
                          )}

                          {/* Ad Content */}
                          <div className="space-y-2">
                            <h3 className="font-semibold text-base leading-tight">{ad.headline}</h3>
                            <p className="text-sm text-gray-600 leading-relaxed">{ad.description}</p>
                            <div className="flex items-center justify-between">
                              <Badge variant="secondary" className="text-xs">{ad.cta}</Badge>
                              {formatData && (
                                <Badge variant="outline" className="text-xs">
                                  {formatData.ratio}
                                </Badge>
                              )}
                            </div>
                          </div>

                          {/* Platform-Specific Preview */}
                          <div className="pt-2 border-t">
                            <PlatformPreview
                              ad={ad}
                              formatData={formatData}
                              getImageUrl={getImageUrl}
                            />
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-12">
                  <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No ads generated yet</h3>
                  <p className="text-gray-600">
                    Go to the Generate tab to create your first ad!
                  </p>
                </div>
              )}

              {/* Download Manager */}
              {generatedAds.length > 0 && (
                <AdDownloadManager
                  ads={generatedAds}
                  getImageUrl={getImageUrl}
                />
              )}
            </TabsContent>

            <TabsContent value="saved" className="space-y-6">
              {/* Storage Stats */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Cloud className="h-5 w-5" />
                    Storage Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-blue-600">{storageStats.totalBatches || 0}</div>
                      <div className="text-sm text-gray-600">Batches</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">{storageStats.totalAds || 0}</div>
                      <div className="text-sm text-gray-600">Total Ads</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-purple-600">
                        {storageStats.storageUsed || 0} MB
                      </div>
                      <div className="text-sm text-gray-600">Storage Used</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Saved Ad Batches */}
              {isLoadingSaved ? (
                <div className="text-center py-12">
                  <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
                  <p>Loading saved ads...</p>
                </div>
              ) : savedBatches.length > 0 ? (
                <div className="space-y-4">
                  {savedBatches.map((batch) => (
                    <Card key={batch.batchId}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="text-lg">{batch.prompt.slice(0, 60)}...</CardTitle>
                            <p className="text-sm text-gray-600 mt-1">
                              {new Date(batch.savedAt).toLocaleDateString()} • {batch.totalAds} ads • {batch.platforms.join(", ")}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => loadSavedAds()}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Refresh
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteBatch(batch.batchId)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                          {batch.ads.slice(0, 4).map((ad: StoredAd) => (
                            <div key={ad.id} className="p-3 border rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <Badge variant="outline" className="capitalize">
                                  {ad.platform}
                                </Badge>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyAdContent(ad)}
                                >
                                  <Copy className="h-3 w-3" />
                                </Button>
                              </div>
                              {ad.media_assets && ad.media_assets.length > 0 && (
                                <div className="aspect-video rounded bg-gray-100 mb-2 overflow-hidden">
                                  <img
                                    src={ad.media_assets[0].url}
                                    alt={ad.headline}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                              )}
                              <h4 className="font-medium text-sm mb-1">{ad.headline}</h4>
                              <p className="text-xs text-gray-600 line-clamp-2">{ad.description}</p>
                            </div>
                          ))}
                        </div>
                        {batch.ads.length > 4 && (
                          <p className="text-sm text-gray-500 mt-3 text-center">
                            +{batch.ads.length - 4} more ads in this batch
                          </p>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <HardDrive className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No saved ads yet</h3>
                  <p className="text-gray-600">
                    Generate some ads and they'll be automatically saved here
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="history" className="space-y-6">
              {history.length > 0 ? (
                <div className="space-y-4">
                  {history.map((entry) => (
                    <Card key={entry.id}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <p className="font-medium line-clamp-2">{entry.prompt}</p>
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline">{entry.adsCount} ads</Badge>
                              <Badge variant="outline">{entry.platforms.length} platforms</Badge>
                              <Badge variant="outline">{entry.imageStyle}</Badge>
                              <span className="text-xs text-gray-500">
                                {new Date(entry.timestamp).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => loadFromHistory(entry)}
                          >
                            Load
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <History className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No history yet</h3>
                  <p className="text-gray-600">
                    Your generated ads will appear here
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
