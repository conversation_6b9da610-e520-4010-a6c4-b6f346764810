"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  FileText, 
  Image as ImageIcon, 
  Package, 
  Loader2,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import JSZip from 'jszip';

interface AdData {
  id: string;
  platform: string;
  headline: string;
  description: string;
  cta: string;
  content: string;
  media_assets?: Array<{
    url: string;
    type: string;
    storage_path?: string;
  }>;
  format_specs?: {
    width: number;
    height: number;
    aspect_ratio: string;
    name: string;
  };
}

interface AdDownloadManagerProps {
  ads: AdData[];
  getImageUrl: (mediaAsset: any) => string | null;
}

export default function AdDownloadManager({ ads, getImageUrl }: AdDownloadManagerProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState<{[key: string]: string}>({});

  const downloadImage = async (ad: AdData) => {
    if (!ad.media_assets || ad.media_assets.length === 0) {
      toast.error('No image available for this ad');
      return;
    }

    setDownloadProgress(prev => ({ ...prev, [`${ad.id}-image`]: 'downloading' }));

    try {
      const imageUrl = getImageUrl(ad.media_assets[0]);
      if (!imageUrl) {
        throw new Error('Image URL not available');
      }

      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error('Failed to fetch image');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${ad.platform}-${ad.format_specs?.name || 'ad'}-${ad.id}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setDownloadProgress(prev => ({ ...prev, [`${ad.id}-image`]: 'completed' }));
      toast.success('Image downloaded successfully');
    } catch (error) {
      setDownloadProgress(prev => ({ ...prev, [`${ad.id}-image`]: 'error' }));
      toast.error('Failed to download image');
      console.error('Download error:', error);
    }
  };

  const downloadText = (ad: AdData) => {
    const textContent = `${ad.headline}\n\n${ad.description}\n\nCall to Action: ${ad.cta}\n\nPlatform: ${ad.platform}\nFormat: ${ad.format_specs?.name || 'Unknown'}\nDimensions: ${ad.format_specs?.width}x${ad.format_specs?.height}px\nAspect Ratio: ${ad.format_specs?.aspect_ratio}`;
    
    const blob = new Blob([textContent], { type: 'text/plain' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${ad.platform}-${ad.format_specs?.name || 'ad'}-${ad.id}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.success('Text content downloaded successfully');
  };

  const downloadCompleteAd = async (ad: AdData) => {
    setDownloadProgress(prev => ({ ...prev, [`${ad.id}-complete`]: 'downloading' }));

    try {
      const zip = new JSZip();
      
      // Add text content
      const textContent = `Ad Content for ${ad.platform}\n\nHeadline: ${ad.headline}\n\nDescription: ${ad.description}\n\nCall to Action: ${ad.cta}\n\nPlatform: ${ad.platform}\nFormat: ${ad.format_specs?.name || 'Unknown'}\nDimensions: ${ad.format_specs?.width}x${ad.format_specs?.height}px\nAspect Ratio: ${ad.format_specs?.aspect_ratio}\n\nGenerated on: ${new Date().toLocaleString()}`;
      zip.file(`${ad.platform}-ad-content.txt`, textContent);

      // Add image if available
      if (ad.media_assets && ad.media_assets.length > 0) {
        const imageUrl = getImageUrl(ad.media_assets[0]);
        if (imageUrl) {
          try {
            const response = await fetch(imageUrl);
            if (response.ok) {
              const blob = await response.blob();
              zip.file(`${ad.platform}-ad-image.jpg`, blob);
            }
          } catch (error) {
            console.error('Error adding image to zip:', error);
          }
        }
      }

      // Generate and download zip
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      const url = window.URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${ad.platform}-${ad.format_specs?.name || 'ad'}-${ad.id}-complete.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      setDownloadProgress(prev => ({ ...prev, [`${ad.id}-complete`]: 'completed' }));
      toast.success('Complete ad package downloaded successfully');
    } catch (error) {
      setDownloadProgress(prev => ({ ...prev, [`${ad.id}-complete`]: 'error' }));
      toast.error('Failed to download complete ad package');
      console.error('Download error:', error);
    }
  };

  const downloadAllAds = async () => {
    if (ads.length === 0) {
      toast.error('No ads to download');
      return;
    }

    setIsDownloading(true);

    try {
      const zip = new JSZip();
      
      // Group ads by platform
      const adsByPlatform = ads.reduce((acc, ad) => {
        if (!acc[ad.platform]) {
          acc[ad.platform] = [];
        }
        acc[ad.platform].push(ad);
        return acc;
      }, {} as {[key: string]: AdData[]});

      // Create folders for each platform
      for (const [platform, platformAds] of Object.entries(adsByPlatform)) {
        const platformFolder = zip.folder(platform);
        
        for (let i = 0; i < platformAds.length; i++) {
          const ad = platformAds[i];
          const adFolder = platformFolder?.folder(`ad-${i + 1}-${ad.format_specs?.name || 'unknown'}`);
          
          // Add text content
          const textContent = `Ad Content\n\nHeadline: ${ad.headline}\n\nDescription: ${ad.description}\n\nCall to Action: ${ad.cta}\n\nPlatform: ${ad.platform}\nFormat: ${ad.format_specs?.name || 'Unknown'}\nDimensions: ${ad.format_specs?.width}x${ad.format_specs?.height}px\nAspect Ratio: ${ad.format_specs?.aspect_ratio}\n\nGenerated on: ${new Date().toLocaleString()}`;
          adFolder?.file('content.txt', textContent);

          // Add image if available
          if (ad.media_assets && ad.media_assets.length > 0) {
            const imageUrl = getImageUrl(ad.media_assets[0]);
            if (imageUrl) {
              try {
                const response = await fetch(imageUrl);
                if (response.ok) {
                  const blob = await response.blob();
                  adFolder?.file('image.jpg', blob);
                }
              } catch (error) {
                console.error(`Error adding image for ad ${ad.id}:`, error);
              }
            }
          }
        }
      }

      // Add summary file
      const summary = `Ad Campaign Summary\n\nTotal Ads: ${ads.length}\nPlatforms: ${Object.keys(adsByPlatform).join(', ')}\nGenerated on: ${new Date().toLocaleString()}\n\n${ads.map((ad, index) => `${index + 1}. ${ad.platform} - ${ad.headline}`).join('\n')}`;
      zip.file('campaign-summary.txt', summary);

      // Generate and download zip
      const zipBlob = await zip.generateAsync({ type: 'blob' });
      const url = window.URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `ad-campaign-${new Date().toISOString().split('T')[0]}.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Downloaded ${ads.length} ads successfully`);
    } catch (error) {
      toast.error('Failed to download all ads');
      console.error('Download error:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  const getDownloadIcon = (adId: string, type: string) => {
    const status = downloadProgress[`${adId}-${type}`];
    switch (status) {
      case 'downloading':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Download className="h-4 w-4" />;
    }
  };

  if (ads.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No ads available for download</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          Download Manager
        </CardTitle>
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-600">
            {ads.length} ad{ads.length > 1 ? 's' : ''} available for download
          </p>
          <Button 
            onClick={downloadAllAds} 
            disabled={isDownloading}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
          >
            {isDownloading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Downloading...
              </>
            ) : (
              <>
                <Package className="h-4 w-4 mr-2" />
                Download All
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {ads.map((ad, index) => (
          <div key={ad.id} className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Badge variant="outline">{ad.platform}</Badge>
                <span className="text-sm font-medium">{ad.headline}</span>
              </div>
              <Badge variant="secondary" className="text-xs">
                {ad.format_specs?.name || 'Unknown Format'}
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadImage(ad)}
                disabled={!ad.media_assets || ad.media_assets.length === 0}
                className="flex items-center gap-1"
              >
                {getDownloadIcon(ad.id, 'image')}
                <ImageIcon className="h-3 w-3" />
                Image
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadText(ad)}
                className="flex items-center gap-1"
              >
                <FileText className="h-3 w-3" />
                Text
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => downloadCompleteAd(ad)}
                className="flex items-center gap-1"
              >
                {getDownloadIcon(ad.id, 'complete')}
                <Package className="h-3 w-3" />
                Complete
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
