"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Heart, MessageCircle, Share, MoreHorizontal, ThumbsUp, Send, Bookmark } from 'lucide-react';
import Image from 'next/image';

interface AdData {
  id: string;
  platform: string;
  headline: string;
  description: string;
  cta: string;
  content: string;
  media_assets?: Array<{
    url: string;
    type: string;
    storage_path?: string;
  }>;
  format_specs?: {
    width: number;
    height: number;
    aspect_ratio: string;
    name: string;
  };
}

interface PlatformPreviewProps {
  ad: AdData;
  formatData?: {
    name: string;
    dimensions: string;
    ratio: string;
    description: string;
    width: number;
    height: number;
  };
  getImageUrl: (mediaAsset: any) => string | null;
}

export default function PlatformPreview({ ad, formatData, getImageUrl }: PlatformPreviewProps) {
  const renderFacebookPreview = () => (
    <div className="bg-white rounded-lg border shadow-sm max-w-md mx-auto">
      {/* Facebook Header */}
      <div className="flex items-center gap-3 p-3 border-b">
        <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
          B
        </div>
        <div className="flex-1">
          <div className="font-semibold text-gray-900 text-sm">Your Brand</div>
          <div className="text-gray-500 text-xs flex items-center gap-1">
            <span>Sponsored</span>
            <span>•</span>
            <span>🌍</span>
          </div>
        </div>
        <MoreHorizontal className="h-5 w-5 text-gray-500" />
      </div>

      {/* Content */}
      <div className="p-3">
        <p className="text-gray-800 text-sm mb-3">{ad.content || ad.description}</p>
      </div>

      {/* Image */}
      {ad.media_assets && ad.media_assets.length > 0 && (
        <div className="relative">
          <img
            src={getImageUrl(ad.media_assets[0]) || ''}
            alt="Ad content"
            className="w-full h-auto object-cover"
            style={{ aspectRatio: formatData?.ratio || '1:1' }}
          />
        </div>
      )}

      {/* CTA Button */}
      <div className="p-3">
        <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium">
          {ad.cta}
        </Button>
      </div>

      {/* Engagement */}
      <div className="flex items-center justify-between px-3 py-2 border-t">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1 text-gray-600">
            <ThumbsUp className="h-4 w-4" />
            <span className="text-xs">Like</span>
          </div>
          <div className="flex items-center gap-1 text-gray-600">
            <MessageCircle className="h-4 w-4" />
            <span className="text-xs">Comment</span>
          </div>
          <div className="flex items-center gap-1 text-gray-600">
            <Share className="h-4 w-4" />
            <span className="text-xs">Share</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderInstagramPreview = () => (
    <div className="bg-white rounded-lg border shadow-sm max-w-md mx-auto">
      {/* Instagram Header */}
      <div className="flex items-center gap-3 p-3">
        <div className="w-8 h-8 bg-gradient-to-tr from-yellow-400 via-red-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-xs">
          B
        </div>
        <div className="flex-1">
          <div className="font-semibold text-gray-900 text-sm">yourbrand</div>
          <div className="text-gray-500 text-xs">Sponsored</div>
        </div>
        <MoreHorizontal className="h-5 w-5 text-gray-900" />
      </div>

      {/* Image */}
      {ad.media_assets && ad.media_assets.length > 0 && (
        <div className="relative">
          <img
            src={getImageUrl(ad.media_assets[0]) || ''}
            alt="Ad content"
            className="w-full h-auto object-cover"
            style={{ aspectRatio: formatData?.ratio || '1:1' }}
          />
        </div>
      )}

      {/* Engagement Icons */}
      <div className="flex items-center justify-between p-3">
        <div className="flex items-center gap-4">
          <Heart className="h-6 w-6 text-gray-900" />
          <MessageCircle className="h-6 w-6 text-gray-900" />
          <Send className="h-6 w-6 text-gray-900" />
        </div>
        <Bookmark className="h-6 w-6 text-gray-900" />
      </div>

      {/* Content */}
      <div className="px-3 pb-3">
        <div className="text-sm text-gray-900 mb-1">
          <span className="font-semibold">yourbrand</span> {ad.content || ad.description}
        </div>
        <Button className="mt-2 bg-blue-500 hover:bg-blue-600 text-white text-xs px-4 py-1 rounded-md">
          {ad.cta}
        </Button>
      </div>
    </div>
  );

  const renderGoogleAdsPreview = () => (
    <div className="bg-white rounded-lg border shadow-sm max-w-md mx-auto">
      {/* Google Ads Header */}
      <div className="flex items-center gap-2 p-2 bg-gray-50 border-b">
        <div className="flex items-center gap-1">
          <div className="w-4 h-4 bg-blue-500 rounded-sm flex items-center justify-center">
            <span className="text-white text-xs font-bold">G</span>
          </div>
          <span className="text-xs text-gray-600">Ad</span>
        </div>
        <div className="text-xs text-gray-500">•</div>
        <div className="text-xs text-gray-600">yourbrand.com</div>
      </div>

      {/* Image */}
      {ad.media_assets && ad.media_assets.length > 0 && (
        <div className="relative">
          <img
            src={getImageUrl(ad.media_assets[0]) || ''}
            alt="Ad content"
            className="w-full h-auto object-cover"
            style={{ aspectRatio: formatData?.ratio || '1.2:1' }}
          />
        </div>
      )}

      {/* Content */}
      <div className="p-3">
        <h3 className="font-semibold text-blue-600 text-sm mb-1 hover:underline cursor-pointer">
          {ad.headline}
        </h3>
        <p className="text-gray-700 text-xs mb-2">{ad.description}</p>
        <div className="flex items-center justify-between">
          <span className="text-green-700 text-xs">yourbrand.com</span>
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white text-xs px-3 py-1">
            {ad.cta}
          </Button>
        </div>
      </div>
    </div>
  );

  const renderLinkedInPreview = () => (
    <div className="bg-white rounded-lg border shadow-sm max-w-md mx-auto">
      {/* LinkedIn Header */}
      <div className="flex items-center gap-3 p-3 border-b">
        <div className="w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center text-white font-bold">
          B
        </div>
        <div className="flex-1">
          <div className="font-semibold text-gray-900 text-sm">Your Brand</div>
          <div className="text-gray-500 text-xs">Company • Promoted</div>
        </div>
        <MoreHorizontal className="h-5 w-5 text-gray-500" />
      </div>

      {/* Content */}
      <div className="p-3">
        <p className="text-gray-800 text-sm mb-3">{ad.content || ad.description}</p>
      </div>

      {/* Image */}
      {ad.media_assets && ad.media_assets.length > 0 && (
        <div className="relative">
          <img
            src={getImageUrl(ad.media_assets[0]) || ''}
            alt="Ad content"
            className="w-full h-auto object-cover"
            style={{ aspectRatio: formatData?.ratio || '1.91:1' }}
          />
        </div>
      )}

      {/* CTA */}
      <div className="p-3">
        <Button className="w-full bg-blue-700 hover:bg-blue-800 text-white text-sm font-medium">
          {ad.cta}
        </Button>
      </div>

      {/* Engagement */}
      <div className="flex items-center justify-between px-3 py-2 border-t">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-1 text-gray-600">
            <ThumbsUp className="h-4 w-4" />
            <span className="text-xs">Like</span>
          </div>
          <div className="flex items-center gap-1 text-gray-600">
            <MessageCircle className="h-4 w-4" />
            <span className="text-xs">Comment</span>
          </div>
          <div className="flex items-center gap-1 text-gray-600">
            <Share className="h-4 w-4" />
            <span className="text-xs">Share</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPreview = () => {
    switch (ad.platform) {
      case 'facebook':
        return renderFacebookPreview();
      case 'instagram':
        return renderInstagramPreview();
      case 'google':
        return renderGoogleAdsPreview();
      case 'linkedin':
        return renderLinkedInPreview();
      default:
        return (
          <div className="bg-gray-100 rounded-lg p-4 text-center">
            <p className="text-gray-600">Preview not available for {ad.platform}</p>
          </div>
        );
    }
  };

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="font-medium text-sm">Platform Preview</h4>
        <Badge variant="outline" className="text-xs">
          {formatData?.name || ad.platform}
        </Badge>
      </div>
      {renderPreview()}
    </div>
  );
}
