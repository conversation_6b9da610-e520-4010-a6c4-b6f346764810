"""
AdMesh Ad Generation API
Provides AI-powered ad generation capabilities for brands with multimodal support
"""

import os
import json
import logging
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field
from openai import OpenAI
from auth.deps import require_role
from firebase.config import get_db
from google.cloud import firestore
from api.services.storage_service import StorageService
import requests

# Configure logging
logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter()

# Initialize OpenAI client
openai_client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Initialize Storage Service
storage_service = StorageService()

class MediaAsset(BaseModel):
    """Represents a generated media asset (image/video)"""
    id: str
    type: str  # "image" or "video"
    url: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    created_at: str

# Platform format specifications
PLATFORM_FORMATS = {
    "instagram": {
        "story": {"width": 1080, "height": 1920, "aspect_ratio": "9:16", "name": "Instagram Story"},
        "post": {"width": 1080, "height": 1080, "aspect_ratio": "1:1", "name": "Instagram Post"},
        "reels": {"width": 1080, "height": 1920, "aspect_ratio": "9:16", "name": "Instagram Reels"}
    },
    "facebook": {
        "feed_post": {"width": 1200, "height": 1200, "aspect_ratio": "1:1", "name": "Facebook Feed Post"},
        "story": {"width": 1080, "height": 1920, "aspect_ratio": "9:16", "name": "Facebook Story"},
        "cover_photo": {"width": 1640, "height": 1025, "aspect_ratio": "16:9", "name": "Facebook Cover Photo"}
    },
    "google": {
        "display_banner": {"width": 728, "height": 90, "aspect_ratio": "8:1", "name": "Google Display Banner"},
        "medium_rectangle": {"width": 300, "height": 250, "aspect_ratio": "1.2:1", "name": "Google Medium Rectangle"},
        "large_rectangle": {"width": 336, "height": 280, "aspect_ratio": "1.2:1", "name": "Google Large Rectangle"},
        "skyscraper": {"width": 160, "height": 600, "aspect_ratio": "4:15", "name": "Google Skyscraper"},
        "responsive_display": {"width": 1200, "height": 628, "aspect_ratio": "1.91:1", "name": "Google Responsive Display"}
    },
    "linkedin": {
        "feed_post": {"width": 1200, "height": 627, "aspect_ratio": "1.91:1", "name": "LinkedIn Feed Post"},
        "single_image": {"width": 1080, "height": 1080, "aspect_ratio": "1:1", "name": "LinkedIn Single Image"},
        "cover_image": {"width": 1128, "height": 191, "aspect_ratio": "5.9:1", "name": "LinkedIn Cover Image"}
    }
}

class AdGenerationRequest(BaseModel):
    product_name: str
    product_description: str
    target_audience: Optional[str] = None
    ad_type: str = "text"  # text, image, video, multimodal
    platform: str = "facebook"  # facebook, instagram
    format: str = "feed_post"  # platform-specific format
    tone: str = "professional"
    keywords: Optional[str] = None
    cta: Optional[str] = None
    additional_info: Optional[str] = None
    num_variations: int = 1  # Changed default to 1, max 3
    # New multimodal fields
    generate_images: bool = False
    generate_videos: bool = False
    image_style: Optional[str] = "professional"  # professional, creative, minimalist, bold
    video_duration: Optional[int] = 15  # seconds
    brand_colors: Optional[List[str]] = None  # hex colors
    brand_fonts: Optional[List[str]] = None
    use_brand_assets: bool = True

class GeneratedAd(BaseModel):
    id: str
    type: str
    content: str
    headline: Optional[str] = None
    description: Optional[str] = None
    cta: Optional[str] = None
    platform: str
    format: str
    # Platform-specific format information
    format_specs: Dict[str, Any] = Field(default_factory=dict)  # width, height, aspect_ratio, name
    dimensions: Dict[str, int] = Field(default_factory=dict)  # width, height
    # New multimodal fields
    media_assets: List[MediaAsset] = Field(default_factory=list)
    generation_metadata: Dict[str, Any] = Field(default_factory=dict)
    platform_adaptations: Dict[str, Dict[str, Any]] = Field(default_factory=dict)

class AdGenerationSession(BaseModel):
    """Represents a complete ad generation session"""
    session_id: str
    brand_id: str
    request: AdGenerationRequest
    generated_ads: List[GeneratedAd]
    status: str  # "pending", "processing", "completed", "failed"
    progress: int = 0  # 0-100
    created_at: str
    completed_at: Optional[str] = None
    error_message: Optional[str] = None

class AdGenerationResponse(BaseModel):
    success: bool
    session_id: str
    ads: List[GeneratedAd] = Field(default_factory=list)
    status: str = "pending"
    progress: int = 0
    message: Optional[str] = None

async def generate_image_with_dalle(prompt: str, style: str = "professional", size: str = "1024x1024", format_specs: Dict[str, Any] = None) -> str:
    """Generate image using DALL-E 3"""
    try:
        # Enhance prompt based on style
        style_prompts = {
            "professional": "professional, clean, corporate style, high quality",
            "creative": "creative, artistic, vibrant colors, innovative design",
            "minimalist": "minimalist, clean lines, simple, elegant",
            "bold": "bold, striking, high contrast, attention-grabbing"
        }

        # Add format-specific guidance to prompt
        format_guidance = ""
        if format_specs:
            aspect_ratio = format_specs.get('aspect_ratio', '1:1')
            format_name = format_specs.get('name', 'standard')
            format_guidance = f", optimized for {format_name} format with {aspect_ratio} aspect ratio"

        enhanced_prompt = f"{prompt}, {style_prompts.get(style, style_prompts['professional'])}{format_guidance}"

        # Determine optimal size based on format specs
        if format_specs:
            width = format_specs.get('width', 1024)
            height = format_specs.get('height', 1024)

            # DALL-E 3 supports specific sizes
            if width == height:
                dalle_size = "1024x1024"
            elif width > height:
                dalle_size = "1792x1024"  # landscape
            else:
                dalle_size = "1024x1792"  # portrait
        else:
            dalle_size = size

        response = openai_client.images.generate(
            model="dall-e-3",
            prompt=enhanced_prompt,
            size=dalle_size,
            quality="hd",
            n=1
        )

        return response.data[0].url
    except Exception as e:
        logger.error(f"DALL-E image generation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate image: {str(e)}")

async def generate_video_prompt(ad_content: str, brand_info: Dict[str, Any]) -> str:
    """Generate a video script/prompt for video generation services"""
    try:
        prompt = f"""
        Create a detailed video script for a {brand_info.get('duration', 15)}-second advertisement video.

        Ad Content: {ad_content}
        Brand: {brand_info.get('brand_name', 'Brand')}
        Style: {brand_info.get('style', 'professional')}

        Include:
        - Scene descriptions
        - Text overlays
        - Timing (in seconds)
        - Visual elements
        - Call-to-action placement

        Format as JSON with scenes array.
        """

        response = openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a video production expert. Create detailed video scripts for advertisements."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=1000
        )

        return response.choices[0].message.content
    except Exception as e:
        logger.error(f"Video script generation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate video script: {str(e)}")

def validate_platform_format(platform: str, format: str) -> bool:
    """Validate that the format is supported for the given platform"""
    if platform not in PLATFORM_FORMATS:
        return False
    return format in PLATFORM_FORMATS[platform]

def get_format_specs(platform: str, format: str) -> Dict[str, Any]:
    """Get format specifications for a platform and format"""
    if not validate_platform_format(platform, format):
        # Return default Facebook feed post specs
        return PLATFORM_FORMATS["facebook"]["feed_post"]
    return PLATFORM_FORMATS[platform][format]

def get_format_optimization_guidelines(platform: str, format: str) -> str:
    """Get format-specific optimization guidelines"""
    guidelines = {
        "facebook": {
            "feed_post": "- Use eye-catching visuals that work well in square format\n- Keep text overlay minimal (less than 20% of image)\n- Focus on clear, benefit-driven messaging\n- Include social proof elements when possible",
            "story": "- Design for vertical viewing on mobile devices\n- Use bold, readable fonts for text overlays\n- Place key information in the center 'safe zone'\n- Create urgency with time-sensitive offers\n- Use interactive elements like polls or questions",
            "cover_photo": "- Design for wide landscape format\n- Include brand logo and key messaging prominently\n- Use high-quality, professional imagery\n- Ensure text is readable at different screen sizes"
        },
        "instagram": {
            "post": "- Prioritize high-quality, visually appealing imagery\n- Use Instagram-native features like hashtags strategically\n- Keep captions engaging but concise\n- Include user-generated content when relevant\n- Use brand-consistent visual style",
            "story": "- Design for full-screen vertical experience\n- Use Instagram story features (stickers, polls, questions)\n- Create thumb-stopping visuals with bold colors\n- Include clear, tappable CTAs\n- Use authentic, behind-the-scenes content",
            "reels": "- Focus on video-first content with engaging hooks\n- Use trending audio and effects\n- Keep text minimal and easy to read quickly\n- Create content that encourages shares and saves\n- Include clear value proposition in first 3 seconds"
        }
    }

    return guidelines.get(platform, {}).get(format, "- Optimize content for the selected format\n- Follow platform best practices\n- Ensure visual elements work well with the aspect ratio")

def create_media_asset(media_url: str, media_type: str) -> MediaAsset:
    """Create MediaAsset object from URL"""
    return MediaAsset(
        id=str(uuid.uuid4()),
        type=media_type,
        url=media_url,
        created_at=datetime.now().isoformat(),
        metadata={
            "generated_at": datetime.now().isoformat(),
            "source": "dall-e-3"
        }
    )

def create_ad_generation_prompt(request: AdGenerationRequest, brand_data: Dict[str, Any] = None) -> str:
    """Create a detailed prompt for OpenAI to generate ads"""
    
    platform_specs = {
        "facebook": {
            "headline_limit": 40,
            "description_limit": 125,
            "best_practices": "Use engaging visuals, clear value propositions, and strong CTAs"
        },
        "instagram": {
            "headline_limit": 30,
            "description_limit": 125,
            "best_practices": "Focus on visual storytelling, use hashtags, keep text minimal"
        },
        "google": {
            "headline_limit": 30,
            "description_limit": 90,
            "best_practices": "Include keywords, be specific about benefits, use ad extensions"
        },
        "linkedin": {
            "headline_limit": 150,
            "description_limit": 600,
            "best_practices": "Professional tone, focus on business value, target decision makers"
        },
        "twitter": {
            "headline_limit": 280,
            "description_limit": 280,
            "best_practices": "Concise messaging, use trending hashtags, encourage engagement"
        },
        "tiktok": {
            "headline_limit": 100,
            "description_limit": 100,
            "best_practices": "Trendy language, video-first content, authentic feel"
        },
        "youtube": {
            "headline_limit": 100,
            "description_limit": 5000,
            "best_practices": "Compelling thumbnails, clear value in first 5 seconds, strong hooks"
        }
    }
    
    platform_info = platform_specs.get(request.platform, platform_specs["facebook"])
    
    # Include brand information if available
    brand_context = ""
    if brand_data:
        brand_context = f"""
BRAND INFORMATION:
- Company: {brand_data.get('company_name', 'N/A')}
- Industry: {brand_data.get('industry', 'N/A')}
- Website: {brand_data.get('website', 'N/A')}
- Brand Colors: {', '.join(brand_data.get('brand_colors', [])) if brand_data.get('brand_colors') else 'N/A'}
- Brand Fonts: {', '.join(brand_data.get('brand_fonts', [])) if brand_data.get('brand_fonts') else 'N/A'}
- Brand Tone: {brand_data.get('tone_of_voice', 'N/A')}
- Existing Target Audience: {brand_data.get('target_audience', 'N/A')}

BRAND GUIDELINES:
- Maintain consistency with the brand's established tone and visual identity
- Use brand colors and fonts when generating visual content
- Align messaging with the company's industry and positioning
- Consider the brand's existing target audience preferences

"""

    # Use brand data to enhance targeting and tone
    effective_tone = brand_data.get('tone_of_voice', request.tone) if brand_data else request.tone
    effective_audience = request.target_audience or (brand_data.get('target_audience') if brand_data else "General audience")

    # Get format specifications
    format_specs = get_format_specs(request.platform, request.format)

    prompt = f"""
You are an expert advertising copywriter specializing in creating high-converting ads for {request.platform.title()}.

{brand_context}Create {request.num_variations} different ad variations for the following product:

FORMAT SPECIFICATIONS:
- Platform: {request.platform.title()}
- Format: {format_specs.get('name', request.format)}
- Dimensions: {format_specs.get('width', 'N/A')}x{format_specs.get('height', 'N/A')} pixels
- Aspect Ratio: {format_specs.get('aspect_ratio', 'N/A')}
- Optimize content for this specific format and dimensions

PRODUCT INFORMATION:
- Product Name: {request.product_name}
- Description: {request.product_description}
- Target Audience: {effective_audience}
- Keywords: {request.keywords or "Not specified"}
- Desired CTA: {request.cta or "Learn More"}
- Additional Context: {request.additional_info or "None"}

PLATFORM REQUIREMENTS ({request.platform.title()}):
- Headline limit: {platform_info['headline_limit']} characters
- Description limit: {platform_info['description_limit']} characters
- Best practices: {platform_info['best_practices']}

TONE: {effective_tone.title()}

AD TYPE: {request.ad_type.title()}

MULTIMODAL FEATURES:
- Generate Images: {request.generate_images}
- Generate Videos: {request.generate_videos}
- Image Style: {request.image_style or 'professional'}
- Video Duration: {request.video_duration or 15} seconds

FORMAT-SPECIFIC OPTIMIZATION:
{get_format_optimization_guidelines(request.platform, request.format)}

INSTRUCTIONS:
1. Create {request.num_variations} unique ad variations
2. Each ad should have a compelling headline, engaging description, and strong call-to-action
3. Use the specified tone throughout
4. Incorporate the target audience and keywords naturally
5. Follow platform-specific best practices and character limits
6. Optimize content specifically for the {format_specs.get('name', request.format)} format
7. Consider the {format_specs.get('aspect_ratio', 'N/A')} aspect ratio when suggesting visual elements
8. Make each variation distinctly different in approach (emotional, logical, urgency-based, etc.)
9. Include relevant emojis where appropriate for the platform and format
10. Ensure ads are compliant with platform advertising policies

OUTPUT FORMAT:
Return a JSON array with {request.num_variations} ad objects, each containing:
{{
  "headline": "Compelling headline under {platform_info['headline_limit']} chars",
  "description": "Engaging description under {platform_info['description_limit']} chars", 
  "cta": "Strong call-to-action",
  "content": "Full ad copy combining headline and description",
  "approach": "Brief description of the approach used (e.g., 'emotional appeal', 'logical benefits', 'urgency-driven')"
}}

Only return valid JSON - no additional text or formatting.
"""
    
    return prompt

def call_openai_for_ads(prompt: str) -> str:
    """Call OpenAI API to generate ads"""
    try:
        response = openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {
                    "role": "system",
                    "content": "You are an expert advertising copywriter who creates high-converting ads. Always respond with valid JSON only."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            temperature=0.8,
            max_tokens=2000
        )

        return response.choices[0].message.content

    except Exception as e:
        logger.error(f"OpenAI API error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to generate ads: {str(e)}")

async def parse_openai_response(response_text: str, request: AdGenerationRequest, session_id: str) -> List[GeneratedAd]:
    """Parse OpenAI response and create GeneratedAd objects with multimodal support"""
    try:
        # Clean the response text
        cleaned_response = response_text.strip()
        if cleaned_response.startswith("```json"):
            cleaned_response = cleaned_response[7:]
        if cleaned_response.endswith("```"):
            cleaned_response = cleaned_response[:-3]

        ads_data = json.loads(cleaned_response)

        generated_ads = []
        for i, ad_data in enumerate(ads_data):
            ad_id = f"ad_{session_id}_{i+1}"
            media_assets = []

            # Get format specifications for image generation
            format_specs = get_format_specs(request.platform, request.format)

            # Generate images if requested
            if request.generate_images and request.ad_type in ["image", "multimodal"]:
                try:
                    image_prompt = f"Advertisement for {request.product_name}: {ad_data.get('content', '')}"
                    image_url = await generate_image_with_dalle(
                        image_prompt,
                        request.image_style or "professional",
                        format_specs=format_specs
                    )

                    # Create media asset directly from URL
                    media_asset = create_media_asset(image_url, "image")
                    media_assets.append(media_asset)

                except Exception as e:
                    logger.warning(f"Failed to generate image for ad {ad_id}: {e}")

            # Generate video script if requested
            if request.generate_videos and request.ad_type in ["video", "multimodal"]:
                try:
                    brand_info = {
                        "brand_name": request.product_name,
                        "duration": request.video_duration or 15,
                        "style": request.image_style or "professional"
                    }
                    video_script = await generate_video_prompt(ad_data.get('content', ''), brand_info)

                    # Store video script in metadata for now
                    # In production, this would integrate with video generation services
                    ad_data["video_script"] = video_script

                except Exception as e:
                    logger.warning(f"Failed to generate video script for ad {ad_id}: {e}")

            generated_ad = GeneratedAd(
                id=ad_id,
                type=request.ad_type,
                content=ad_data.get("content", ""),
                headline=ad_data.get("headline", ""),
                description=ad_data.get("description", ""),
                cta=ad_data.get("cta", request.cta or "Learn More"),
                platform=request.platform,
                format=request.format,
                format_specs=format_specs,
                dimensions={
                    "width": format_specs.get("width", 1200),
                    "height": format_specs.get("height", 1200)
                },
                media_assets=media_assets,
                generation_metadata={
                    "generated_at": datetime.now().isoformat(),
                    "model_used": "gpt-4",
                    "style": request.image_style,
                    "video_script": ad_data.get("video_script"),
                    "format_name": format_specs.get("name", request.format)
                }
            )
            generated_ads.append(generated_ad)

        return generated_ads

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse OpenAI response as JSON: {str(e)}")
        logger.error(f"Response text: {response_text}")
        raise HTTPException(status_code=500, detail="Failed to parse generated ads")
    except Exception as e:
        logger.error(f"Error processing OpenAI response: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process generated ads")

async def save_ad_generation_session(session: AdGenerationSession):
    """Save ad generation session to Firestore with comprehensive tracking"""
    try:
        db = get_db()
        session_data = session.model_dump()

        # Convert datetime objects to strings for Firestore
        session_data["created_at"] = firestore.SERVER_TIMESTAMP
        if session_data.get("completed_at"):
            session_data["completed_at"] = firestore.SERVER_TIMESTAMP

        db.collection("ad_generation_sessions").document(session.session_id).set(session_data)
        logger.info(f"Saved ad generation session {session.session_id} for brand {session.brand_id}")

    except Exception as e:
        logger.error(f"Failed to save ad generation session: {str(e)}")
        # Don't raise exception here as it's not critical for the main functionality

async def update_session_progress(session_id: str, progress: int, status: str = None):
    """Update session progress in real-time"""
    try:
        db = get_db()
        update_data = {"progress": progress}
        if status:
            update_data["status"] = status

        db.collection("ad_generation_sessions").document(session_id).update(update_data)
        logger.debug(f"Updated session {session_id} progress to {progress}%")

    except Exception as e:
        logger.error(f"Failed to update session progress: {str(e)}")

async def get_brand_data(brand_id: str) -> Dict[str, Any]:
    """Retrieve brand data from Firestore to inform ad generation"""
    try:
        db = get_db()
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            return {}

        brand_data = brand_doc.to_dict()

        # Extract relevant brand information for ad generation
        return {
            "company_name": brand_data.get("company_name", ""),
            "industry": brand_data.get("industry", ""),
            "logo_url": brand_data.get("logo_url", ""),
            "website": brand_data.get("website", ""),
            "brand_colors": brand_data.get("brand_colors", []),
            "brand_fonts": brand_data.get("brand_fonts", []),
            "tone_of_voice": brand_data.get("tone_of_voice", "professional"),
            "target_audience": brand_data.get("target_audience", "")
        }

    except Exception as e:
        logger.error(f"Failed to retrieve brand data: {str(e)}")
        return {}

@router.post("/test-generate", response_model=AdGenerationResponse)
async def test_generate_ads(request: AdGenerationRequest):
    """Test ad generation without authentication or validation"""

    session_id = str(uuid.uuid4())
    logger.info(f"Test ad generation: {request.product_name} (session: {session_id})")

    try:
        # Validate basic request
        if not request.product_name or not request.product_description:
            raise HTTPException(
                status_code=400,
                detail="Product name and description are required"
            )

        # Limit number of variations
        if request.num_variations > 3:
            request.num_variations = 3
        elif request.num_variations < 1:
            request.num_variations = 1

        # Create prompt for OpenAI with empty brand data
        prompt = create_ad_generation_prompt(request, {})

        # Call OpenAI API
        openai_response = call_openai_for_ads(prompt)

        # Parse response into GeneratedAd objects with multimodal support
        generated_ads = await parse_openai_response(openai_response, request, session_id)

        logger.info(f"Successfully generated {len(generated_ads)} test ads")

        return AdGenerationResponse(
            success=True,
            session_id=session_id,
            ads=generated_ads,
            status="completed",
            progress=100,
            message=f"Successfully generated {len(generated_ads)} test ad variations"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in test ad generation: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while generating test ads"
        )

@router.post("/generate", response_model=AdGenerationResponse)
async def generate_ads(
    request: AdGenerationRequest,
    user_data = Depends(require_role("brand"))
):
    """Generate AI-powered ads with authentication"""
    return await _generate_ads_internal(request, user_data)

async def _generate_ads_internal(request: AdGenerationRequest, user_data: dict):
    """Internal ad generation logic"""

    brand_id = user_data["uid"]
    session_id = str(uuid.uuid4())
    logger.info(f"Ad generation request from brand {brand_id}: {request.product_name} (session: {session_id})")

    try:
        # Check if brand has completed onboarding
        db = get_db()
        brand_ref = db.collection("brands").document(brand_id)
        brand_doc = brand_ref.get()

        if not brand_doc.exists:
            raise HTTPException(
                status_code=400,
                detail="Please complete your brand onboarding before generating ads"
            )

        brand_data = brand_doc.to_dict()
        onboarding_status = brand_data.get("onboarding_status", "brand")

        if onboarding_status != "completed":
            raise HTTPException(
                status_code=400,
                detail="Please complete your brand onboarding before generating ads"
            )

        # Validate request
        if not request.product_name or not request.product_description:
            raise HTTPException(
                status_code=400,
                detail="Product name and description are required"
            )

        # Validate platform and format
        if not validate_platform_format(request.platform, request.format):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid format '{request.format}' for platform '{request.platform}'"
            )

        # Limit number of variations (max 3 for enhanced system)
        if request.num_variations > 3:
            request.num_variations = 3
        elif request.num_variations < 1:
            request.num_variations = 1

        # Get brand data to inform generation
        brand_info = await get_brand_data(brand_id)

        # Merge brand data with request if use_brand_assets is True
        if request.use_brand_assets:
            if not request.brand_colors and brand_info.get("brand_colors"):
                request.brand_colors = brand_info["brand_colors"]
            if not request.brand_fonts and brand_info.get("brand_fonts"):
                request.brand_fonts = brand_info["brand_fonts"]
            if not request.target_audience and brand_info.get("target_audience"):
                request.target_audience = brand_info["target_audience"]

        # Create initial session
        session = AdGenerationSession(
            session_id=session_id,
            brand_id=brand_id,
            request=request,
            generated_ads=[],
            status="processing",
            progress=0,
            created_at=datetime.now().isoformat()
        )

        # Save initial session
        await save_ad_generation_session(session)

        # Update progress
        await update_session_progress(session_id, 10, "processing")

        # Create prompt for OpenAI with brand data
        prompt = create_ad_generation_prompt(request, brand_info)

        # Update progress
        await update_session_progress(session_id, 30)

        # Call OpenAI API
        openai_response = call_openai_for_ads(prompt)

        # Update progress
        await update_session_progress(session_id, 50)

        # Parse response into GeneratedAd objects with multimodal support
        generated_ads = await parse_openai_response(openai_response, request, session_id)

        # Validate ads for compliance
        generated_ads = await validate_ad_content(generated_ads)

        # Update progress
        await update_session_progress(session_id, 90)

        # Save ads to Firebase Storage
        try:
            format_specs = get_format_specs(request.platform, request.format)
            storage_metadata = {
                "prompt": request.product_description,
                "platforms": [request.platform],
                "format": request.format,
                "format_specs": format_specs,
                "image_style": request.image_style or "professional",
                "ad_type": request.ad_type,
                "session_id": session_id,
                "brand_id": brand_id
            }

            # Convert GeneratedAd objects to dictionaries for storage
            ads_for_storage = [ad.model_dump() for ad in generated_ads]

            storage_result = await storage_service.save_ads_to_storage(
                brand_id=brand_id,
                session_id=session_id,
                ads=ads_for_storage,
                metadata=storage_metadata
            )

            logger.info(f"Saved {storage_result['total_saved']} ads to storage for brand {brand_id}")

        except Exception as storage_error:
            logger.error(f"Failed to save ads to storage: {str(storage_error)}")
            # Continue without failing the entire request

        # Update session with results
        session.generated_ads = generated_ads
        session.status = "completed"
        session.progress = 100
        session.completed_at = datetime.now().isoformat()

        # Save final session
        await save_ad_generation_session(session)

        logger.info(f"Successfully generated {len(generated_ads)} ads for brand {brand_id}")

        return AdGenerationResponse(
            success=True,
            session_id=session_id,
            ads=generated_ads,
            status="completed",
            progress=100,
            message=f"Successfully generated {len(generated_ads)} ad variations"
        )

    except HTTPException:
        # Update session with error
        try:
            await update_session_progress(session_id, 0, "failed")
        except:
            pass
        raise
    except Exception as e:
        logger.error(f"Unexpected error in ad generation: {str(e)}")
        # Update session with error
        try:
            await update_session_progress(session_id, 0, "failed")
        except:
            pass
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while generating ads"
        )

@router.get("/session/{session_id}")
async def get_session_status(
    session_id: str,
    user_data = Depends(require_role("brand"))
):
    """Get the status and progress of an ad generation session"""

    brand_id = user_data["uid"]

    try:
        db = get_db()
        session_ref = db.collection("ad_generation_sessions").document(session_id)
        session_doc = session_ref.get()

        if not session_doc.exists:
            raise HTTPException(status_code=404, detail="Session not found")

        session_data = session_doc.to_dict()

        # Verify the session belongs to the requesting brand
        if session_data.get("brand_id") != brand_id:
            raise HTTPException(status_code=403, detail="Access denied")

        return {
            "success": True,
            "session": session_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get session status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve session status")

@router.get("/history")
async def get_ad_generation_history(
    user_data = Depends(require_role("brand")),
    limit: int = 20,
    status: Optional[str] = None
):
    """Get ad generation history for a brand with filtering options"""

    brand_id = user_data["uid"]

    try:
        db = get_db()
        query = (
            db.collection("ad_generation_sessions")
            .where("brand_id", "==", brand_id)
        )

        # Add status filter if provided
        if status:
            query = query.where("status", "==", status)

        sessions = (
            query
            .order_by("created_at", direction=firestore.Query.DESCENDING)
            .limit(limit)
            .stream()
        )

        history = []
        for session in sessions:
            session_data = session.to_dict()
            session_data["session_id"] = session.id

            # Add summary statistics
            session_data["summary"] = {
                "num_ads": len(session_data.get("generated_ads", [])),
                "has_images": any(
                    ad.get("media_assets", [])
                    for ad in session_data.get("generated_ads", [])
                ),
                "platforms": list(set(
                    ad.get("platform", "unknown")
                    for ad in session_data.get("generated_ads", [])
                ))
            }

            history.append(session_data)

        return {"success": True, "history": history, "total": len(history)}

    except Exception as e:
        logger.error(f"Failed to get ad generation history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve ad generation history")

@router.delete("/session/{session_id}")
async def delete_session(
    session_id: str,
    user_data = Depends(require_role("brand"))
):
    """Delete an ad generation session and its associated media assets"""

    brand_id = user_data["uid"]

    try:
        db = get_db()
        session_ref = db.collection("ad_generation_sessions").document(session_id)
        session_doc = session_ref.get()

        if not session_doc.exists:
            raise HTTPException(status_code=404, detail="Session not found")

        session_data = session_doc.to_dict()

        # Verify the session belongs to the requesting brand
        if session_data.get("brand_id") != brand_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # Media assets are just URLs, no storage cleanup needed
        logger.info(f"Session {session_id} deleted")

        # Delete the session document
        session_ref.delete()

        return {"success": True, "message": "Session deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete session: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete session")

# Platform compliance checks
def check_platform_compliance(ad_content: str, platform: str) -> Dict[str, Any]:
    """Check if ad content complies with platform policies"""
    compliance_issues = []
    warnings = []

    # Common compliance checks
    prohibited_words = [
        "guaranteed", "miracle", "instant", "free money", "get rich quick",
        "lose weight fast", "cure", "secret", "limited time only"
    ]

    content_lower = ad_content.lower()

    for word in prohibited_words:
        if word in content_lower:
            compliance_issues.append(f"Contains potentially prohibited word: '{word}'")

    # Platform-specific checks
    if platform == "facebook":
        if len(ad_content) > 125:
            warnings.append("Facebook ads perform better with shorter text")
        if "click here" in content_lower:
            warnings.append("Avoid generic CTAs like 'click here' on Facebook")

    elif platform == "google":
        if not any(char.isupper() for char in ad_content):
            warnings.append("Google Ads benefit from proper capitalization")
        if len(ad_content.split()) < 3:
            warnings.append("Google Ads should have descriptive content")

    elif platform == "linkedin":
        if "professional" not in content_lower and "business" not in content_lower:
            warnings.append("LinkedIn ads should emphasize professional value")

    return {
        "compliant": len(compliance_issues) == 0,
        "issues": compliance_issues,
        "warnings": warnings,
        "score": max(0, 100 - (len(compliance_issues) * 20) - (len(warnings) * 5))
    }

async def validate_ad_content(ads: List[GeneratedAd]) -> List[GeneratedAd]:
    """Validate and enhance ads with compliance checks"""
    validated_ads = []

    for ad in ads:
        # Run compliance check
        compliance = check_platform_compliance(ad.content, ad.platform)

        # Add compliance metadata
        ad.generation_metadata["compliance"] = compliance

        # Log compliance issues
        if not compliance["compliant"]:
            logger.warning(f"Compliance issues for ad {ad.id}: {compliance['issues']}")

        validated_ads.append(ad)

    return validated_ads

class AdGenerationMetrics(BaseModel):
    """Metrics for ad generation performance tracking"""
    total_sessions: int
    successful_sessions: int
    failed_sessions: int
    average_generation_time: float
    total_ads_generated: int
    platform_distribution: Dict[str, int]
    compliance_score_average: float

@router.get("/metrics")
async def get_ad_generation_metrics(
    user_data = Depends(require_role("brand")),
    days: int = 30
):
    """Get ad generation metrics for a brand"""

    brand_id = user_data["uid"]

    try:
        db = get_db()

        # Calculate date range
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # Query sessions
        sessions = (
            db.collection("ad_generation_sessions")
            .where("brand_id", "==", brand_id)
            .where("created_at", ">=", start_date.isoformat())
            .where("created_at", "<=", end_date.isoformat())
            .stream()
        )

        total_sessions = 0
        successful_sessions = 0
        failed_sessions = 0
        total_ads = 0
        platform_counts = {}
        compliance_scores = []
        generation_times = []

        for session in sessions:
            session_data = session.to_dict()
            total_sessions += 1

            status = session_data.get("status", "unknown")
            if status == "completed":
                successful_sessions += 1

                # Count ads and platforms
                ads = session_data.get("generated_ads", [])
                total_ads += len(ads)

                for ad in ads:
                    platform = ad.get("platform", "unknown")
                    platform_counts[platform] = platform_counts.get(platform, 0) + 1

                    # Collect compliance scores
                    compliance = ad.get("generation_metadata", {}).get("compliance", {})
                    if "score" in compliance:
                        compliance_scores.append(compliance["score"])

                # Calculate generation time
                created_at = session_data.get("created_at")
                completed_at = session_data.get("completed_at")
                if created_at and completed_at:
                    try:
                        start_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                        end_time = datetime.fromisoformat(completed_at.replace('Z', '+00:00'))
                        generation_time = (end_time - start_time).total_seconds()
                        generation_times.append(generation_time)
                    except:
                        pass

            elif status == "failed":
                failed_sessions += 1

        metrics = AdGenerationMetrics(
            total_sessions=total_sessions,
            successful_sessions=successful_sessions,
            failed_sessions=failed_sessions,
            average_generation_time=sum(generation_times) / len(generation_times) if generation_times else 0,
            total_ads_generated=total_ads,
            platform_distribution=platform_counts,
            compliance_score_average=sum(compliance_scores) / len(compliance_scores) if compliance_scores else 0
        )

        return {
            "success": True,
            "metrics": metrics.model_dump(),
            "period_days": days
        }

    except Exception as e:
        logger.error(f"Failed to get ad generation metrics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve metrics")

# Platform-specific configuration for ad adaptation
PLATFORM_CONFIGS = {
    "facebook": {
        "headline_limit": 40,
        "description_limit": 125,
        "image_sizes": ["1200x628", "1080x1080"],
        "video_specs": {"max_duration": 240, "aspect_ratios": ["16:9", "1:1", "4:5"]},
        "best_practices": ["Use clear CTAs", "Include social proof", "Mobile-first design"]
    },
    "instagram": {
        "headline_limit": 30,
        "description_limit": 125,
        "image_sizes": ["1080x1080", "1080x1350"],
        "video_specs": {"max_duration": 60, "aspect_ratios": ["1:1", "4:5", "9:16"]},
        "best_practices": ["Visual-first content", "Use hashtags", "Stories format"]
    },
    "linkedin": {
        "headline_limit": 150,
        "description_limit": 600,
        "image_sizes": ["1200x627", "1080x1080"],
        "video_specs": {"max_duration": 600, "aspect_ratios": ["16:9", "1:1"]},
        "best_practices": ["Professional tone", "Industry insights", "B2B focus"]
    },
    "google": {
        "headline_limit": 30,
        "description_limit": 90,
        "image_sizes": ["1200x628", "300x250", "728x90", "336x280", "160x600"],
        "video_specs": {"max_duration": 30, "aspect_ratios": ["16:9", "1:1"]},
        "best_practices": ["Keyword optimization", "Clear value prop", "Landing page alignment", "Mobile-first design"],
        "logo": "google-ads",
        "brand_color": "#4285f4",
        "platform_name": "Google Ads"
    },
    "tiktok": {
        "headline_limit": 100,
        "description_limit": 100,
        "image_sizes": ["1080x1920", "1080x1080"],
        "video_specs": {"max_duration": 60, "aspect_ratios": ["9:16", "1:1"]},
        "best_practices": ["Vertical video", "Trending sounds", "Native feel"]
    }
}

class PlatformAdaptationRequest(BaseModel):
    session_id: str
    ad_id: str
    target_platforms: List[str]
    maintain_brand_consistency: bool = True

@router.post("/adapt-platforms")
async def adapt_ad_for_platforms(
    request: PlatformAdaptationRequest,
    user_data = Depends(require_role("brand"))
):
    """Adapt a generated ad for multiple platforms with platform-specific optimization"""

    brand_id = user_data["uid"]

    try:
        # Get the original session and ad
        db = get_db()
        session_ref = db.collection("ad_generation_sessions").document(request.session_id)
        session_doc = session_ref.get()

        if not session_doc.exists:
            raise HTTPException(status_code=404, detail="Session not found")

        session_data = session_doc.to_dict()

        # Verify the session belongs to the requesting brand
        if session_data.get("brand_id") != brand_id:
            raise HTTPException(status_code=403, detail="Access denied")

        # Find the specific ad
        original_ad = None
        for ad in session_data.get("generated_ads", []):
            if ad.get("id") == request.ad_id:
                original_ad = ad
                break

        if not original_ad:
            raise HTTPException(status_code=404, detail="Ad not found")

        # Get brand data for consistency
        brand_info = await get_brand_data(brand_id) if request.maintain_brand_consistency else {}

        adapted_ads = []

        for platform in request.target_platforms:
            if platform not in PLATFORM_CONFIGS:
                logger.warning(f"Unknown platform: {platform}")
                continue

            platform_config = PLATFORM_CONFIGS[platform]

            # Create platform-specific adaptation prompt
            adaptation_prompt = f"""
            Adapt the following ad for {platform.title()} platform:

            Original Ad:
            Headline: {original_ad.get('headline', '')}
            Description: {original_ad.get('description', '')}
            CTA: {original_ad.get('cta', '')}

            Platform Requirements:
            - Headline limit: {platform_config['headline_limit']} characters
            - Description limit: {platform_config['description_limit']} characters
            - Best practices: {', '.join(platform_config['best_practices'])}

            Brand Guidelines:
            - Company: {brand_info.get('company_name', 'N/A')}
            - Industry: {brand_info.get('industry', 'N/A')}
            - Tone: {brand_info.get('tone_of_voice', 'professional')}

            Create an adapted version that:
            1. Fits platform character limits
            2. Follows platform best practices
            3. Maintains brand consistency
            4. Optimizes for platform-specific user behavior

            Return JSON with: headline, description, cta, platform_notes
            """

            try:
                response = openai_client.chat.completions.create(
                    model="gpt-4",
                    messages=[
                        {"role": "system", "content": "You are a platform-specific ad optimization expert."},
                        {"role": "user", "content": adaptation_prompt}
                    ],
                    temperature=0.7,
                    max_tokens=500
                )

                # Parse the adaptation response
                adaptation_text = response.choices[0].message.content
                cleaned_response = adaptation_text.strip()
                if cleaned_response.startswith("```json"):
                    cleaned_response = cleaned_response[7:]
                if cleaned_response.endswith("```"):
                    cleaned_response = cleaned_response[:-3]

                adaptation_data = json.loads(cleaned_response.strip())

                # Create adapted ad
                adapted_ad = {
                    "id": f"{request.ad_id}_{platform}",
                    "original_ad_id": request.ad_id,
                    "platform": platform,
                    "headline": adaptation_data.get("headline", ""),
                    "description": adaptation_data.get("description", ""),
                    "cta": adaptation_data.get("cta", ""),
                    "platform_notes": adaptation_data.get("platform_notes", ""),
                    "platform_config": platform_config,
                    "adapted_at": datetime.now().isoformat()
                }

                adapted_ads.append(adapted_ad)

            except Exception as e:
                logger.error(f"Failed to adapt ad for {platform}: {e}")
                continue

        # Update the original session with platform adaptations
        if adapted_ads:
            # Find and update the original ad with platform adaptations
            for i, ad in enumerate(session_data["generated_ads"]):
                if ad.get("id") == request.ad_id:
                    if "platform_adaptations" not in session_data["generated_ads"][i]:
                        session_data["generated_ads"][i]["platform_adaptations"] = {}

                    for adapted_ad in adapted_ads:
                        platform = adapted_ad["platform"]
                        session_data["generated_ads"][i]["platform_adaptations"][platform] = adapted_ad
                    break

            # Save updated session
            session_ref.set(session_data)

        return {
            "success": True,
            "adapted_ads": adapted_ads,
            "message": f"Successfully adapted ad for {len(adapted_ads)} platforms"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to adapt ad for platforms: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to adapt ad for platforms")


@router.get("/saved-ads")
async def get_saved_ads(
    limit: int = Query(50, description="Maximum number of batches to return"),
    current_user: dict = Depends(require_role(["brand"]))
):
    """Get saved ads for the current brand"""
    try:
        brand_id = current_user["uid"]

        result = await storage_service.get_saved_ads(brand_id, limit)

        if result["success"]:
            return {
                "success": True,
                "batches": result["batches"],
                "total_batches": result["total_batches"],
                "total_ads": result["total_ads"]
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to retrieve saved ads")

    except Exception as e:
        logger.error(f"Error getting saved ads: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get saved ads: {str(e)}")


@router.delete("/saved-ads/{batch_id}")
async def delete_saved_ad_batch(
    batch_id: str,
    current_user: dict = Depends(require_role(["brand"]))
):
    """Delete a saved ad batch"""
    try:
        brand_id = current_user["uid"]

        result = await storage_service.delete_ad_batch(brand_id, batch_id)

        if result["success"]:
            return {
                "success": True,
                "message": f"Successfully deleted batch {batch_id}",
                "deleted_files": result["total_deleted"]
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to delete ad batch")

    except Exception as e:
        logger.error(f"Error deleting ad batch: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete ad batch: {str(e)}")


@router.get("/storage-stats")
async def get_storage_stats(
    current_user: dict = Depends(require_role(["brand"]))
):
    """Get storage usage statistics for the current brand"""
    try:
        brand_id = current_user["uid"]

        stats = await storage_service.get_storage_stats(brand_id)

        return {
            "success": True,
            "stats": stats
        }

    except Exception as e:
        logger.error(f"Error getting storage stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get storage stats: {str(e)}")
